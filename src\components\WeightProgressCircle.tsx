import React, { useEffect } from 'react';
import { View, Text, StyleSheet } from 'react-native';
import Animated, {
  useSharedValue,
  useAnimatedProps,
  withTiming,
  withSpring,
  interpolate,
  Easing,
} from 'react-native-reanimated';
import Svg, { Circle, Defs, LinearGradient, Stop } from 'react-native-svg';
import { Colors } from '../constants/Colors';

const AnimatedCircle = Animated.createAnimatedComponent(Circle);

interface WeightProgressCircleProps {
  progress: number; // 0-100
  size?: number;
  strokeWidth?: number;
  currentWeight: number;
  targetWeight: number;
  weightLost: number;
  isOnTrack: boolean;
  style?: any;
}

const WeightProgressCircle: React.FC<WeightProgressCircleProps> = ({
  progress,
  size = 200,
  strokeWidth = 12,
  currentWeight,
  targetWeight,
  weightLost,
  isOnTrack,
  style,
}) => {
  const animatedProgress = useSharedValue(0);
  const scaleValue = useSharedValue(0.8);

  const radius = (size - strokeWidth) / 2;
  const circumference = 2 * Math.PI * radius;

  useEffect(() => {
    // Animate progress with spring
    animatedProgress.value = withTiming(progress, {
      duration: 1500,
      easing: Easing.out(Easing.cubic),
    });

    // Scale animation for entrance
    scaleValue.value = withSpring(1, {
      damping: 15,
      stiffness: 150,
    });
  }, [progress]);

  const animatedProps = useAnimatedProps(() => {
    const strokeDashoffset = interpolate(
      animatedProgress.value,
      [0, 100],
      [circumference, 0]
    );

    return {
      strokeDashoffset,
      transform: [{ scale: scaleValue.value }],
    };
  });

  const getProgressColor = () => {
    if (progress >= 75) return Colors.brand; // Dark green for excellent progress
    if (progress >= 50) return Colors.brand; // Dark green for good progress
    if (progress >= 25) return Colors.brandSecondary; // Olive green for moderate progress
    return Colors.brandTertiary; // Light green for low progress - NO RED
  };

  const getMotivationalMessage = () => {
    if (progress >= 90) return 'Almost there! 🎉';
    if (progress >= 75) return 'Excellent progress! 💪';
    if (progress >= 50) return 'Great job! Keep going! 🔥';
    if (progress >= 25) return 'Good start! Stay focused! 💯';
    return 'Your journey begins! 🌟';
  };

  const getStatusIcon = () => {
    if (progress >= 75) return '🏆';
    if (progress >= 50) return '⭐';
    if (progress >= 25) return '📈';
    return '🎯';
  };

  return (
    <View style={[styles.container, style]}>
      <Animated.View style={[styles.circleContainer, { transform: [{ scale: scaleValue }] }]}>
        <Svg width={size} height={size} style={styles.svg}>
          <Defs>
            <LinearGradient id="progressGradient" x1="0%" y1="0%" x2="100%" y2="100%">
              <Stop offset="0%" stopColor={getProgressColor()} stopOpacity="1" />
              <Stop offset="100%" stopColor={getProgressColor()} stopOpacity="0.7" />
            </LinearGradient>
          </Defs>
          
          {/* Background Circle */}
          <Circle
            cx={size / 2}
            cy={size / 2}
            r={radius}
            stroke={Colors.brandMuted}
            strokeWidth={strokeWidth}
            fill="none"
          />
          
          {/* Progress Circle */}
          <AnimatedCircle
            cx={size / 2}
            cy={size / 2}
            r={radius}
            stroke="url(#progressGradient)"
            strokeWidth={strokeWidth}
            fill="none"
            strokeLinecap="round"
            strokeDasharray={circumference}
            animatedProps={animatedProps}
            transform={`rotate(-90 ${size / 2} ${size / 2})`}
          />
        </Svg>

        {/* Center Content - REDESIGNED FOR CLARITY */}
        <View style={styles.centerContent}>
          {/* Main Progress Display */}
          <View style={styles.mainProgressContainer}>
            <Text style={styles.progressText}>{Math.round(progress)}%</Text>
            <Text style={styles.progressLabel}>Complete</Text>
          </View>

          {/* Weight Display - Simplified */}
          <View style={styles.weightDisplayContainer}>
            <View style={styles.currentWeightDisplay}>
              <Text style={styles.currentWeightValue}>{currentWeight.toFixed(1)}</Text>
              <Text style={styles.currentWeightLabel}>Current</Text>
            </View>
            <View style={styles.targetWeightDisplay}>
              <Text style={styles.targetWeightValue}>{targetWeight.toFixed(1)}</Text>
              <Text style={styles.targetWeightLabel}>Target</Text>
            </View>
          </View>
        </View>
      </Animated.View>

      {/* Weight Lost Display - MOVED OUTSIDE CIRCLE */}
      <View style={styles.weightLostContainer}>
        <Text style={[styles.weightLostValue, { color: getProgressColor() }]}>
          -{weightLost.toFixed(1)} kg
        </Text>
        <Text style={styles.weightLostLabel}>Lost</Text>
      </View>

      {/* Status Indicator - REDESIGNED WITH GREEN COLORS ONLY */}
      <View style={[styles.statusIndicator, {
        backgroundColor: isOnTrack ? Colors.brand : Colors.brandSecondary,
        borderColor: isOnTrack ? Colors.brand : Colors.brandSecondary
      }]}>
        <Text style={styles.statusText}>
          {isOnTrack ? '✓ On Track' : '⚠ Needs Focus'}
        </Text>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    alignItems: 'center',
    justifyContent: 'center',
    gap: 16, // Add spacing between elements
  },
  circleContainer: {
    position: 'relative',
    alignItems: 'center',
    justifyContent: 'center',
  },
  svg: {
    position: 'absolute',
  },
  centerContent: {
    alignItems: 'center',
    justifyContent: 'center',
    position: 'absolute',
    width: '100%', // Ensure proper width
  },

  // REDESIGNED CENTER CONTENT STYLES
  mainProgressContainer: {
    alignItems: 'center',
    marginBottom: 8,
  },
  progressText: {
    fontSize: 36, // Larger for better visibility
    fontWeight: '900',
    color: Colors.brand,
    lineHeight: 40, // Prevent text overlap
    letterSpacing: -1,
  },
  progressLabel: {
    fontSize: 12,
    fontWeight: '600',
    color: Colors.mutedForeground,
    marginTop: 2,
    textTransform: 'uppercase',
    letterSpacing: 0.5,
  },

  // REDESIGNED WEIGHT DISPLAY
  weightDisplayContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    width: '80%', // Control width to prevent overflow
    marginTop: 4,
  },
  currentWeightDisplay: {
    alignItems: 'center',
    flex: 1,
  },
  currentWeightValue: {
    fontSize: 14,
    fontWeight: '700',
    color: Colors.foreground,
    lineHeight: 16,
  },
  currentWeightLabel: {
    fontSize: 10,
    fontWeight: '500',
    color: Colors.mutedForeground,
    marginTop: 2,
  },
  targetWeightDisplay: {
    alignItems: 'center',
    flex: 1,
  },
  targetWeightValue: {
    fontSize: 14,
    fontWeight: '700',
    color: Colors.brand,
    lineHeight: 16,
  },
  targetWeightLabel: {
    fontSize: 10,
    fontWeight: '500',
    color: Colors.mutedForeground,
    marginTop: 2,
  },

  // WEIGHT LOST - MOVED OUTSIDE CIRCLE
  weightLostContainer: {
    alignItems: 'center',
    marginTop: 12,
    paddingHorizontal: 16,
    paddingVertical: 8,
    backgroundColor: 'rgba(107, 124, 90, 0.1)',
    borderRadius: 20,
    borderWidth: 1,
    borderColor: 'rgba(107, 124, 90, 0.2)',
  },
  weightLostValue: {
    fontSize: 20,
    fontWeight: '800',
    lineHeight: 24,
  },
  weightLostLabel: {
    fontSize: 11,
    fontWeight: '600',
    color: Colors.mutedForeground,
    marginTop: 2,
    textTransform: 'uppercase',
    letterSpacing: 0.5,
  },

  // REDESIGNED STATUS INDICATOR
  statusIndicator: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    marginTop: 12,
    borderWidth: 1,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  statusText: {
    fontSize: 12,
    fontWeight: '700',
    color: Colors.white,
    textAlign: 'center',
  },
});

export default WeightProgressCircle;
